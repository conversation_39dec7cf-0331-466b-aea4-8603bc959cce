<script setup lang="ts">
import { useLocalizedCurrencies } from '~/api/car/enums/Currency'
import { useLocalizedRegionalSpecifications } from '~/api/car/enums/RegionalSpecification'
import type { Car } from '~/api/car/types/Car'

defineProps<{
  car: Car
}>()

const regionalSpecifications = useLocalizedRegionalSpecifications()
const currencies = useLocalizedCurrencies()

function formatNumber(val: number): string {
  return val.toLocaleString()
}
</script>

<template>
  <VCard elevation="3" rounded="lg">
    <template v-if="car.preview_images.length">
      <!-- Primary Image -->
      <VImg
        :src="car.preview_images[0].url"
        height="200"
        cover
        class="primary-image"
      ></VImg>

      <!-- Thumbnails Row -->
      <VRow no-gutters class="mt-1">
        <VCol
          v-for="(img, i) in car.preview_images.slice(1, 4)"
          :key="i"
          cols="4"
          :class="{
            'pe-1': i < 2,
          }"
        >
          <VImg :src="img.url" height="76" cover class="rounded-sm">
            <VOverlay
              v-if="i === 2 && car.images_count > 4"
              absolute
              contained
              class="d-flex align-center justify-center"
              scrim="rgba(0, 0, 0, 0.5)"
              :model-value="true"
            >
              <VCard
                color="black"
                class="d-flex justify-center align-center px-2"
              >
                <VIcon color="white" size="14" class="me-1"
                  >mdi-image-multiple
                </VIcon>
                <span class="text-white text-caption">{{
                  car.images_count
                }}</span>
              </VCard>
            </VOverlay>
          </VImg>
        </VCol>
      </VRow>
    </template>

    <!-- Car Info Section -->
    <VCardText>
      <div class="text-h6 font-weight-bold">
        {{ formatNumber(car.price) }} {{ currencies[car.currency] }}
      </div>

      <div class="text-caption text-grey-darken-1 mb-1">
        {{ car.car_manufacturer.name_ar }} ·
        {{ regionalSpecifications[car.regional_specifications] }} ·
        {{ car.car_model.name_ar }}
      </div>

      <div class="text-body-2 d-flex align-center mb-2">
        <VIcon size="18" class="me-1" color="primary">mdi-calendar</VIcon>
        {{ car.year }}
        <template v-if="car.status === 'used'">
          <VIcon size="18" class="ms-4 me-1" color="primary"
            >mdi-speedometer</VIcon
          >
          {{ formatNumber(car.travel_distance_in_km) }}
          {{ $t('enums.units.km.short') }}
        </template>
      </div>

      <!-- Action Buttons -->
      <VCardActions class="pa-0 mt-2">
        <VBtn
          width="50%"
          color="primary"
          variant="tonal"
          prepend-icon="mdi-phone"
          >{{ $t('Call') }}</VBtn
        >
        <VBtn
          width="50%"
          color="success"
          variant="tonal"
          prepend-icon="mdi-whatsapp"
          >{{ $t('WhatsApp') }}</VBtn
        >
      </VCardActions>
    </VCardText>
  </VCard>
</template>
