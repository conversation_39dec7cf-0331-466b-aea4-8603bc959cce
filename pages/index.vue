<script setup lang="ts">
import { useGetPaginatedCarsQuery } from '~/api/car/queries'

const { data: cars, suspense } = useGetPaginatedCarsQuery()

onServerPrefetch(suspense)
</script>

<template>
  <VContainer class="py-6">
    <VRow justify="center" justify-sm="start">
      <VCol
        v-for="car in cars.data"
        :key="car.id"
        cols="12"
        sm="6"
        md="6"
        lg="4"
        xl="3"
      >
        <CarCard :car="car" />
      </VCol>
    </VRow>
  </VContainer>
</template>
