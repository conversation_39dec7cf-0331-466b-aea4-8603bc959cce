import { defineVuetifyConfiguration } from 'vuetify-nuxt-module/custom-configuration'

export default defineVuetifyConfiguration({
  theme: {
    defaultTheme: 'light',

    themes: {
      dark: {
        dark: true,
        colors: {
          background: '#003244',
          surface: '#0077a0',
          primary: '#20a7e2',
          secondary: '#ebfaff',
          info: '#2073e2',
          warning: '#e2bb20',
          error: '#e22420',
          success: '#48e220',
          neutral: '#506d72',
        },
      },

      light: {
        dark: false,
        colors: {
          background: '#ebfaff',
          surface: '#ffffff',
          primary: '#20a7e2',
          secondary: '#003244',
          info: '#2073e2',
          warning: '#e2bb20',
          error: '#e22420',
          success: '#48e220',
          neutral: '#506d72',
        },
      },
    },
  },
})
