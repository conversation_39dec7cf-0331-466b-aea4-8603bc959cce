import { useI18n } from 'vue-i18n'

const drivetrainSystemValues = ['fwd', 'rwd', '4wd', 'awd'] as const

export const useLocalizedDrivetrainSystems = () => {
  const { t } = useI18n()
  return drivetrainSystemValues.reduce(
    (acc, value) => {
      acc[value] = t(`enums.drivetrain_system.${value}`)
      return acc
    },
    {} as Record<(typeof drivetrainSystemValues)[number], string>,
  )
}

export type DrivetrainSystem = (typeof drivetrainSystemValues)[number]
