import { useI18n } from 'vue-i18n'

const transmissionTypeValues = [
  'automatic',
  'manual',
  'tiptronic',
  'cvt',
  'dct',
] as const

export const useLocalizedTransmissionTypes = () => {
  const { t } = useI18n()
  return transmissionTypeValues.reduce(
    (acc, value) => {
      acc[value] = t(`enums.transmission_type.${value}`)
      return acc
    },
    {} as Record<(typeof transmissionTypeValues)[number], string>,
  )
}

export type TransmissionType = (typeof transmissionTypeValues)[number]
