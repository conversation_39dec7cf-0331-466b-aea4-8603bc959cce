import { useI18n } from 'vue-i18n'

const fuelTypeValues = ['petrol', 'diesel', 'electric', 'hybrid'] as const

export const useLocalizedFuelTypes = () => {
  const { t } = useI18n()
  return fuelTypeValues.reduce(
    (acc, value) => {
      acc[value] = t(`enums.fuel_type.${value}`)
      return acc
    },
    {} as Record<(typeof fuelTypeValues)[number], string>,
  )
}

export type FuelType = (typeof fuelTypeValues)[number]
