import { useI18n } from 'vue-i18n'

const featureCategoryValues = [
  'safety_and_security',
  'comfort',
  'exterior',
  'entertainment',
  'other',
] as const

export const useLocalizedFeatureCategories = () => {
  const { t } = useI18n()
  return featureCategoryValues.reduce(
    (acc, value) => {
      acc[value] = t(`enums.feature_category.${value}`)
      return acc
    },
    {} as Record<(typeof featureCategoryValues)[number], string>,
  )
}

export type FeatureCategory = (typeof featureCategoryValues)[number]
