import { useI18n } from 'vue-i18n'

const regionalSpecificationValues = [
  'gcc',
  'american',
  'canadian',
  'european',
  'japanese',
  'korean',
  'chinese',
  'other',
] as const

export const useLocalizedRegionalSpecifications = () => {
  const { t } = useI18n()
  return regionalSpecificationValues.reduce(
    (acc, value) => {
      acc[value] = t(`enums.regional_specification.${value}`)
      return acc
    },
    {} as Record<(typeof regionalSpecificationValues)[number], string>,
  )
}

export type RegionalSpecification = (typeof regionalSpecificationValues)[number]
