import type { Car, PaginatedCar } from '~/api/car/types/Car'
import { makeApiRequest } from '~/api/client'
import type { ApiResponse } from '~/api/types/ApiResponse'
import type { PaginatedResponse } from '~/api/types/PaginatedResponse'

export const getPaginatedCars = async (): Promise<
  PaginatedResponse<PaginatedCar>
> => {
  return makeApiRequest<PaginatedResponse<PaginatedCar>>('/cars')
}

export const getCar = async (id: Car['id']): Promise<ApiResponse<Car>> => {
  return makeApiRequest<ApiResponse<Car>>(`/cars/${id}`)
}
